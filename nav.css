.navbar-custom {
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
}
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: #333 !important;
    letter-spacing: 1px;
}
.navbar-nav .nav-link {
    color: #333 !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    transition: color 0.3s ease;
}
.navbar-nav .nav-link:hover {
    color: #007bff !important;
}
.navbar-icons {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.navbar-icons .icon-link {
    color: #333;
    font-size: 1.2rem;
    text-decoration: none;
    transition: color 0.3s ease;
}
.navbar-icons .icon-link:hover {
    color: #007bff;
}
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23f0f0f0" width="1200" height="600"/><text x="600" y="300" text-anchor="middle" font-size="24" fill="%23999">Bike Image Placeholder</text></svg>');
    background-size: cover;
    background-position: center;
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}
.hero-content h1 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}
.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}