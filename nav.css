.navbar-custom {
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
}
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: #333 !important;
    letter-spacing: 1px;
}
.navbar-nav .nav-link {
    color: #333 !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    transition: color 0.3s ease;
}
.navbar-nav .nav-link:hover {
    color: #007bff !important;
}
.navbar-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}
.navbar-icons .icon-link {
    color: #333;
    font-size: 1.2rem;
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}
.navbar-icons .icon-link:hover {
    color: #007bff;
}
.icon-link>.bi {
    flex-shrink: 0;
    width: 1em;
    height: 24px;
    fill: currentcolor;
    transition: .2s ease-in-out transform;
}